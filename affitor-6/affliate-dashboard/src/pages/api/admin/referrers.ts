import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Authorization header required" });
    }

    const token = authHeader.replace("Bearer ", "");

    // Extract query parameters
    const {
      page = 1,
      pageSize = 100, // Get more for dropdown
      search = "",
      sort = "id:ASC"
    } = req.query;

    // Use StrapiAdminClient to fetch referrers
    const data = await StrapiAdminClient.getReferrers({
      page: Number(page),
      pageSize: Number(pageSize),
      search: search as string,
      sort: sort as string,
    }, token) as any;

    return res.status(200).json(data);
  } catch (error: any) {
    console.error("Error fetching admin referrers:", error);
    return res.status(error.response?.status || 500).json({
      error: error.response?.data?.message || "Failed to fetch referrers",
    });
  }
}
